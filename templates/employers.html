{% extends "base.html" %} {% block title %}Employers{% endblock %} {% block content %}

<!-- Modern Hero Section -->
<section class="employers-hero-modern py-5">
  <div class="container">
    <div class="row align-items-center min-vh-50">
      <div class="col-lg-8 mx-auto text-center">
        <div class="hero-content-modern" data-aos="fade-up">
          <h1 class="employers-hero-title mb-4">
            Discover Amazing
            <span class="hero-gradient-text">Companies</span>
            <br />Building the Future
          </h1>
          <p class="employers-hero-subtitle mb-5">
            Connect with innovative organizations, explore company cultures, and find your perfect workplace match.
            From cutting-edge startups to industry leaders, discover opportunities that align with your career goals.
          </p>

          <!-- Modern Stats -->
          <div class="hero-stats-modern d-flex justify-content-center gap-5 mb-5" data-aos="fade-up" data-aos-delay="200">
            <div class="stat-item-modern">
              <div class="stat-number-modern">{{ all_employers|length }}+</div>
              <div class="stat-label-modern">Companies</div>
            </div>
            <div class="stat-item-modern">
              <div class="stat-number-modern">{{ total_open_positions }}+</div>
              <div class="stat-label-modern">Open Positions</div>
            </div>
            <div class="stat-item-modern">
              <div class="stat-number-modern">50+</div>
              <div class="stat-label-modern">Industries</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Subtle Floating Elements -->
  <div class="floating-elements-subtle">
    <div class="floating-card-subtle floating-card-1">
      <i class="bi bi-rocket-takeoff"></i>
    </div>
    <div class="floating-card-subtle floating-card-2">
      <i class="bi bi-people"></i>
    </div>
    <div class="floating-card-subtle floating-card-3">
      <i class="bi bi-trophy"></i>
    </div>
  </div>
</section>

<!-- Modern Search Section -->
<section class="employers-search-modern py-5">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-8 col-md-10">
        <div class="search-card-modern" data-aos="fade-up" data-aos-delay="300">
          <div class="search-header-modern text-center mb-4">
            <h3 class="search-title-modern">Find Your Next Employer</h3>
            <p class="search-subtitle-modern">Search by company name, industry, or location</p>
          </div>

          <div class="search-input-wrapper">
            <div class="search-input-group-modern">
              <i class="bi bi-search search-icon-modern"></i>
              <input
                type="text"
                class="search-input-modern"
                id="keyword-input"
                name="keyword-input"
                placeholder="Search companies, industries, locations..."
                hx-get="/filteremp"
                hx-target="#employers-list"
                hx-trigger="keyup changed delay:300ms"
                hx-indicator="#search-loading"
              />
              <button type="button" class="search-btn-modern">
                <i class="bi bi-arrow-right"></i>
              </button>
            </div>

            <!-- Search Loading Indicator -->
            <div class="text-center mt-3">
              <div id="search-loading" class="htmx-indicator">
                <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
                <span class="text-muted">Searching companies...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Companies Section -->
<section class="employers-listing-section py-5 less-limited-width-content">
  <div class="container">

    <!-- Section Header -->
    <div class="section-header text-center mb-5">
      <h2 class="section-title">
        <span class="gradient-text">Featured Companies</span>
      </h2>
      <p class="section-subtitle">
        Explore opportunities with companies that are actively hiring and growing
      </p>
      <div class="section-divider"></div>
    </div>

    <!-- Employers Grid -->
    <div id="employers-list" class="row g-4">
      {% for employer in all_employers %}
      <div class="col-xl-4 col-lg-6 col-md-6 col-12" data-aos="fade-up" data-aos-delay="{{ (loop.index0 * 100) % 600 }}">
        <div class="employer-card-clean">
          <a href="{{url_for('employer',employer_id=employer.employer_id)}}" class="employer-link-clean">
            <!-- Card Header -->
            <div class="employer-header-clean">
              <div class="employer-logo-clean">
                {% if employer.employer_logo_url %}
                <img
                  src="{{ employer.employer_logo_url }}"
                  class="company-logo-clean"
                  alt="{{ employer.employer_name }}"
                  onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                />
                {% endif %}
                <div class="company-logo-fallback-clean" {% if employer.employer_logo_url %}style="display: none"{% endif %}>
                  {{ (employer.employer_name or 'C')[0]|upper }}
                </div>
              </div>

              <!-- Open Positions Badge - Animated -->
              {% if employer.open_positions > 0 %}
              <div class="positions-badge-clean">
                <i class="bi bi-briefcase me-1"></i>
                {{ employer.open_positions }} Open Positions
              </div>
              {% endif %}
            </div>

            <!-- Company Info -->
            <div class="employer-content-clean">
              <h3 class="company-name-clean">{{ employer.employer_name }}</h3>

              <!-- Company Details -->
              <div class="company-details-clean">
                {% if employer.employer_industry %}
                <div class="detail-row-clean">
                  <i class="bi bi-diagram-3 me-2"></i>
                  <span class="detail-text-clean">{{ employer.employer_industry }}</span>
                </div>
                {% endif %}

                {% if employer.employer_headcount %}
                <div class="detail-row-clean">
                  <i class="bi bi-people me-2"></i>
                  <span class="detail-text-clean">{{ employer.employer_headcount }}</span>
                </div>
                {% endif %}

                {% if employer.headquarter %}
                <div class="detail-row-clean">
                  <i class="bi bi-geo-alt me-2"></i>
                  <span class="detail-text-clean">{{ employer.headquarter }}</span>
                </div>
                {% endif %}
              </div>
            </div>

            <!-- Clean Footer -->
            <div class="employer-footer-clean">
              <span class="view-text-clean">View Company</span>
              <i class="bi bi-arrow-right"></i>
            </div>
          </a>
        </div>
      </div>
      {% endfor %}
    </div>

    <!-- No Results Message -->
    {% if not all_employers %}
    <div class="row">
      <div class="col-12">
        <div class="no-results-card text-center py-5">
          <i class="bi bi-building text-muted mb-3" style="font-size: 3rem;"></i>
          <h4>No companies found</h4>
          <p class="text-muted">Try adjusting your search criteria or check back later for new companies!</p>
          <a href="/jobs" class="btn btn-primary">
            <i class="bi bi-briefcase me-2"></i>
            Explore Job Opportunities
          </a>
        </div>
      </div>
    </div>
    {% endif %}
  </div>
</section>

{% endblock %}
